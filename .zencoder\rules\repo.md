---
description: Repository Information Overview
alwaysApply: true
---

# UFU2 Information

## Summary
UFU2 is a sophisticated Windows desktop application designed for Algerian business registration and client management. Built with WPF and .NET 8.0, it implements a robust MVVM architecture with MaterialDesign theming, Arabic RTL support, and comprehensive business logic for managing client data, activities, and regulatory compliance.

## Structure
- **Commands**: MVVM command implementations
- **Common**: Shared utilities, error handling, converters, and extensions
- **Controls**: Custom WPF controls like InteractiveCropRectangle
- **Converters**: Value converters for UI data transformation
- **Database**: SQLite schema definitions and reference data
- **Models**: Data models for clients, activities, and business entities
- **Resources**: UI styling, themes, fonts, and visual assets
- **Services**: Business logic, data access, and application services
- **ViewModels**: MVVM view models with smart batching
- **Views**: XAML UI definitions and user controls
- **Windows**: Custom window implementations

## Language & Runtime
**Language**: C#
**Version**: .NET 8.0
**Framework**: WPF (Windows Presentation Foundation)
**Build System**: MSBuild
**Package Manager**: NuGet

## Dependencies
**Main Dependencies**:
- MaterialDesignThemes (5.2.1)
- MaterialDesignColors (5.2.1)
- Microsoft.Data.Sqlite (9.0.7)
- Dapper (2.1.66)
- Newtonsoft.Json (13.0.3)
- Microsoft.Extensions.Caching.Memory (9.0.7)

## Build & Installation
```bash
dotnet restore "UFU2.csproj"
dotnet build "UFU2.csproj" -c Release
```

## Database
**Type**: SQLite
**Schema Files**:
- Database/UFU2_Schema.sql
- Database/APP_Schema.sql
- Database/Archive_Schema.sql
**Reference Data**:
- Database/activity_Type.json
- Database/craft_Type.json
- Database/cpi_Location.json

## Architecture
**Pattern**: MVVM (Model-View-ViewModel)
**Key Components**:
- ServiceLocator: Dependency injection container
- BaseViewModel: Sophisticated base class with priority-based PropertyChanged batching
- DatabaseService: SQLite operations with Dapper ORM
- ClientDatabaseService: Client CRUD operations
- ValidationService: Business rule validation

## Features
- Arabic RTL interface with proper internationalization
- Light/Dark theme support with MaterialDesign
- Client registration and management
- Activity tracking for Commercial, Craft, and Professional activities
- Regulatory compliance validation
- Image management with interactive cropping
- Performance optimization with smart batching (60-120 FPS)
- Memory management and leak detection