# UFU2 Technology Stack

## Core Framework
- **.NET 8.0**: Target framework with Windows-specific features
- **WPF (Windows Presentation Foundation)**: UI framework for desktop application
- **C#**: Primary programming language with nullable reference types enabled
- **XAML**: UI markup language for views and styling

## Key Dependencies
- **MaterialDesignThemes 5.2.1**: Modern Material Design UI components and theming
- **MaterialDesignColors 5.2.1**: Color palette for Material Design
- **MaterialDesignThemes.MahApps 5.2.1**: Additional Material Design components
- **Microsoft.Data.Sqlite 9.0.7**: SQLite database provider for .NET
- **Dapper 2.1.66**: Lightweight ORM for database operations
- **Newtonsoft.Json 13.0.3**: JSON serialization and deserialization
- **Microsoft.Extensions.Caching.Memory 9.0.7**: In-memory caching capabilities

## Database Technology
- **SQLite**: Embedded database engine
- **Three-Database Architecture**:
  - `UFU2_ClientData.db`: Primary operational client data
  - `UFU2_ReferenceData.db`: Static reference data (activity codes, locations)
  - `UFU2_ArchiveData.db`: Audit logs and historical data
- **Dapper ORM**: Micro-ORM for efficient database operations
- **Schema Management**: SQL files for database schema definition

## Development Tools
- **Visual Studio 2022**: Primary IDE (version 17.14+)
- **Git**: Version control system
- **NuGet**: Package management
- **Windows 10/11**: Target operating system

## Performance & Optimization
- **Smart Batching**: Custom PropertyChanged notification batching (60-120 FPS)
- **Memory Management**: Custom ResourceManager and WeakEventManager
- **Connection Pooling**: Efficient database connection management
- **Async/Await**: Non-blocking operations throughout the application