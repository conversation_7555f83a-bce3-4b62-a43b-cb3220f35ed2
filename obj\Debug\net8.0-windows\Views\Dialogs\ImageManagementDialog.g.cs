﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5E3A3672FE808AE5964853A8FE0B2F1217529CC3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.MahApps;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using UFU2.Common.Converters;
using UFU2.Controls;
using UFU2.Views.UserControls;


namespace UFU2.Views.Dialogs {
    
    
    /// <summary>
    /// ImageManagementDialog
    /// </summary>
    public partial class ImageManagementDialog : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 11 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal UFU2.Views.Dialogs.ImageManagementDialog ImageManagementDialogControl;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.DialogHost ImageManagementDialogHost;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadImage;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CropImageButton;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup shortcutPopup;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Viewbox PreviewContainer;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image PreviewImage;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ScaleTransform ImageScaleTransform;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ScaleTransform PostCropVisualScaleTransform;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.RotateTransform ImageRotateTransform;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.TranslateTransform ImageTranslateTransform;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal UFU2.Controls.InteractiveCropRectangle InteractiveCropGuide;
        
        #line default
        #line hidden
        
        
        #line 355 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomInButton;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ZoomSlider;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomOutButton;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RotateClockwiseButton;
        
        #line default
        #line hidden
        
        
        #line 429 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider RotationSlider;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RotateCounterclockwiseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UFU2;V1.0.0.0;component/views/dialogs/imagemanagementdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ImageManagementDialogControl = ((UFU2.Views.Dialogs.ImageManagementDialog)(target));
            
            #line 20 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.ImageManagementDialogControl.KeyDown += new System.Windows.Input.KeyEventHandler(this.ImageManagementDialogControl_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ImageManagementDialogHost = ((MaterialDesignThemes.Wpf.DialogHost)(target));
            return;
            case 3:
            this.LoadImage = ((System.Windows.Controls.Button)(target));
            
            #line 88 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.LoadImage.Click += new System.Windows.RoutedEventHandler(this.LoadImage_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CropImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 127 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.CropImageButton.Click += new System.Windows.RoutedEventHandler(this.CropImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 148 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Button_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.shortcutPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 8:
            this.PreviewContainer = ((System.Windows.Controls.Viewbox)(target));
            return;
            case 9:
            this.PreviewImage = ((System.Windows.Controls.Image)(target));
            
            #line 255 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.PreviewImage.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.PreviewImage_MouseDown);
            
            #line default
            #line hidden
            
            #line 256 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.PreviewImage.MouseLeave += new System.Windows.Input.MouseEventHandler(this.PreviewImage_MouseLeave);
            
            #line default
            #line hidden
            
            #line 257 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.PreviewImage.MouseMove += new System.Windows.Input.MouseEventHandler(this.PreviewImage_MouseMove);
            
            #line default
            #line hidden
            
            #line 258 "..\..\..\..\..\Views\Dialogs\ImageManagementDialog.xaml"
            this.PreviewImage.MouseUp += new System.Windows.Input.MouseButtonEventHandler(this.PreviewImage_MouseUp);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ImageScaleTransform = ((System.Windows.Media.ScaleTransform)(target));
            return;
            case 11:
            this.PostCropVisualScaleTransform = ((System.Windows.Media.ScaleTransform)(target));
            return;
            case 12:
            this.ImageRotateTransform = ((System.Windows.Media.RotateTransform)(target));
            return;
            case 13:
            this.ImageTranslateTransform = ((System.Windows.Media.TranslateTransform)(target));
            return;
            case 14:
            this.InteractiveCropGuide = ((UFU2.Controls.InteractiveCropRectangle)(target));
            return;
            case 15:
            this.ZoomInButton = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.ZoomSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 17:
            this.ZoomOutButton = ((System.Windows.Controls.Button)(target));
            return;
            case 18:
            this.RotateClockwiseButton = ((System.Windows.Controls.Button)(target));
            return;
            case 19:
            this.RotationSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 20:
            this.RotateCounterclockwiseButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

