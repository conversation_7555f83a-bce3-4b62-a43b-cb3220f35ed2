# UFU2 Project Structure

## Root Level Files
- **UFU2.sln**: Visual Studio solution file
- **UFU2.csproj**: Main project file with dependencies and build configuration
- **App.xaml/cs**: Application entry point and global exception handling
- **MainWindow.xaml/cs**: Main application window with custom chrome
- **AssemblyInfo.cs**: Assembly metadata and version information

## Core Directories

### /ViewModels
- **BaseViewModel.cs**: Advanced base class with smart batching system (60-120 FPS)
- **NewClientViewModel.cs**: Main client registration logic (large file ~3000+ lines)
- **ImageManagementViewModel.cs**: WYSIWYG image editing functionality
- **AddActivityDialogViewModel.cs**: Activity management dialog
- **[15+ specialized ViewModels]**: Dialog and feature-specific ViewModels

### /Views
- **NewClientView.xaml**: Main client registration UI
- **Dialogs/**: Modal dialog views for various operations
- **NewClient/**: Client-specific user controls and components
- **UserControls/**: Reusable UI components

### /Services
- **ServiceLocator.cs**: Dependency injection container
- **DatabaseService.cs**: Core SQLite operations with Dapper
- **ClientDatabaseService.cs**: Client-specific CRUD operations
- **ThemeManager.cs**: MaterialDesign theme management
- **UIDGenerationService.cs**: Business UID generation logic
- **ClientValidationService.cs**: Business rule validation
- **[40+ specialized services]**: Performance, memory, search, and business services

### /Models
- **ClientCreationData.cs**: Client data transfer objects
- **DatabaseEntities.cs**: Database entity models
- **ActivityModel.cs**: Activity-related data models
- **ValidationResultModels.cs**: Validation result containers
- **[20+ specialized models]**: Business domain models

### /Common
- **ErrorManager.cs**: Centralized error handling with Arabic localization
- **LoggingService.cs**: Session-based structured logging
- **ValidationMessages.cs**: Validation message constants
- **Behaviors/**: WPF behaviors for UI interactions
- **Converters/**: Value converters for data binding
- **Extensions/**: Extension methods and utilities
- **Models/**: Common data models
- **Utilities/**: Helper utilities and calculations

### /Resources
- **Styles/**: XAML style definitions for UI components
  - ButtonStyles.xaml, TextBoxStyles.xaml, CardStyles.xaml, etc.
- **Themes/**: Light/Dark theme resource dictionaries
- **Tokens/**: Design system tokens and constants
- **Font/**: Custom fonts for Arabic text support
- **[Images & Icons]**: Visual assets and icons

### /Database
- **UFU2_Schema.sql**: Complete SQLite schema (443 lines)
- **APP_Schema.sql**: Application-specific schema
- **Archive_Schema.sql**: Audit logging schema
- **activity_Type.json**: 4000+ Algerian activity codes
- **craft_Type.json**: Craft type definitions
- **cpi_Location.json**: CPI location data (Wilaya/Daira)

### /Documentation
- **UFU2-Codebase-Analysis.md**: Comprehensive technical documentation
- **[40+ documentation files]**: Architecture, performance, and implementation guides

### /Commands
- **RelayCommand.cs**: Generic command implementation with logging

### /Controls
- **InteractiveCropRectangle.cs**: Custom image cropping control

### /Converters
- **GenderToImageConverter.cs**: Gender-based image selection
- **ProfileImageConverter.cs**: Profile image handling

### /Windows
- **ConfirmationWindow.xaml**: Custom confirmation dialogs

## Build Output
- **bin/**: Compiled binaries and runtime files
- **obj/**: Intermediate build files

## Key Architecture Patterns
- **MVVM**: Strict separation with BaseViewModel inheritance
- **Service Locator**: Centralized dependency injection
- **Three-Database**: Separate databases for different data types
- **Smart Batching**: Performance optimization for UI updates
- **Material Design**: Consistent UI theming and styling
- **Arabic RTL**: Proper right-to-left layout support