# UFU2 Project Overview

## Project Purpose
UFU2 is a sophisticated Windows desktop application designed for Algerian business registration and client management. It serves as a comprehensive system for managing client data, business activities, and regulatory compliance in the Algerian business registration market.

## Key Features
- **Client Management**: Complete CRUD operations for client information with Arabic/French name support
- **Activity Management**: Support for Commercial (Main/Secondary), Craft, and Professional activities
- **Regulatory Compliance**: Built-in validation for Algerian business registration requirements
- **Document Management**: File check states and business rule validation
- **Arabic RTL Support**: Primary Arabic interface with proper right-to-left layout
- **Audit Logging**: Complete change tracking and compliance records
- **Performance Optimization**: Advanced UI performance with 60-120 FPS animations

## Business Domain
- **Target Market**: Algerian business registration offices and consultants
- **Primary Language**: Arabic (RTL) with French secondary support
- **Regulatory Focus**: Algerian commercial, craft, and professional activity registration
- **Data Management**: Client information, business activities, payment tracking, document compliance

## Architecture
- **Pattern**: MVVM (Model-View-ViewModel) with Service Locator dependency injection
- **Database**: Three-database SQLite architecture (Client, Reference, Archive)
- **UI Framework**: WPF with MaterialDesign theming
- **Performance**: Smart batching system for optimal UI responsiveness