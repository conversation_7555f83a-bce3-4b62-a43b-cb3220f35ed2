{"Version": 1, "WorkspaceRootPath": "E:\\UserFiles\\Projects\\UFU2\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|e:\\userfiles\\projects\\ufu2\\views\\newclient\\nactivitydetailview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{20EE55DE-3680-49FD-AC57-5581E2D049CF}|UFU2.csproj|solutionrelative:views\\newclient\\nactivitydetailview.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "NActivityDetailView.xaml.cs", "DocumentMoniker": "E:\\UserFiles\\Projects\\UFU2\\Views\\NewClient\\NActivityDetailView.xaml.cs", "RelativeDocumentMoniker": "Views\\NewClient\\NActivityDetailView.xaml.cs", "ToolTip": "E:\\UserFiles\\Projects\\UFU2\\Views\\NewClient\\NActivityDetailView.xaml.cs", "RelativeToolTip": "Views\\NewClient\\NActivityDetailView.xaml.cs", "ViewState": "AgIAAOEAAAAAAAAAAIA4wPAAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T12:51:55.948Z", "EditorCaption": ""}]}]}]}