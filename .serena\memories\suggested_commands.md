# UFU2 Development Commands

## Build Commands
```powershell
# Build the solution in Debug mode
dotnet build UFU2.sln --configuration Debug

# Build the solution in Release mode
dotnet build UFU2.sln --configuration Release

# Clean the solution
dotnet clean UFU2.sln

# Restore NuGet packages
dotnet restore UFU2.sln
```

## Run Commands
```powershell
# Run the application in Debug mode
dotnet run --project UFU2.csproj --configuration Debug

# Run the application in Release mode
dotnet run --project UFU2.csproj --configuration Release

# Run from Visual Studio (F5 for Debug, Ctrl+F5 for Release)
```

## Package Management
```powershell
# Add a new package
dotnet add UFU2.csproj package PackageName

# Remove a package
dotnet remove UFU2.csproj package PackageName

# Update packages
dotnet restore UFU2.csproj --force

# List installed packages
dotnet list UFU2.csproj package
```

## Database Commands
```powershell
# The application automatically creates and manages SQLite databases:
# - UFU2_ClientData.db (created on first run)
# - UFU2_ReferenceData.db (created on first run)
# - UFU2_ArchiveData.db (created on first run)

# Database schema is managed through embedded SQL files:
# - Database/UFU2_Schema.sql
# - Database/APP_Schema.sql
# - Database/Archive_Schema.sql
```

## Development Workflow
```powershell
# 1. Open solution in Visual Studio
start UFU2.sln

# 2. Build and run for development
dotnet build UFU2.sln --configuration Debug
dotnet run --project UFU2.csproj --configuration Debug

# 3. For production build
dotnet build UFU2.sln --configuration Release
```

## Windows Utility Commands
```powershell
# List files and directories
Get-ChildItem
dir

# Navigate directories
Set-Location "path"
cd "path"

# Find files
Get-ChildItem -Recurse -Name "*.cs"

# Search in files
Select-String -Path "*.cs" -Pattern "search_term"

# Git commands
git status
git add .
git commit -m "message"
git push
git pull
```

## Visual Studio Shortcuts
- **F5**: Start debugging
- **Ctrl+F5**: Start without debugging
- **Ctrl+Shift+B**: Build solution
- **Ctrl+Shift+F**: Find in files
- **F12**: Go to definition
- **Ctrl+K, Ctrl+D**: Format document

## Performance Monitoring
The application includes built-in performance monitoring:
- Startup time tracking
- Memory usage monitoring
- UI responsiveness metrics
- Database performance tracking
- Logging overhead analysis

## Testing
Currently no automated test framework is configured. Manual testing through:
- Running the application in Debug mode
- Using Visual Studio debugger
- Monitoring application logs
- Testing UI interactions manually