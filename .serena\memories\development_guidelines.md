# UFU2 Development Guidelines

## Core Development Principles

### 1. MVVM Architecture (Mandatory)
- **ViewModels**: Must inherit from `BaseViewModel` for smart batching
- **Views**: XAML-only with minimal code-behind logic
- **Models**: Plain data classes for data transfer and entities
- **Commands**: Use `RelayCommand` for all UI interactions
- **Data Binding**: Extensive use of WPF data binding patterns

### 2. Service Layer Pattern
- **Service Locator**: Use `ServiceLocator.GetService<T>()` for dependency injection
- **Business Logic**: Keep business logic in dedicated service classes
- **Database Access**: All database operations through `DatabaseService`
- **Validation**: Use `ClientValidationService` for business rule validation

### 3. Database Best Practices
- **Three-Database Architecture**: Client, Reference, and Archive databases
- **Async Operations**: All database calls must be async/await
- **Parameterized Queries**: Always use parameterized queries with Dapper
- **Transactions**: Wrap multi-step operations in database transactions
- **Error Handling**: Use retry logic for transient database failures

### 4. Performance Optimization
- **Smart Batching**: Leverage BaseViewModel's 60-120 FPS batching system
- **Memory Management**: Implement IDisposable and use WeakEventManager
- **UI Thread Safety**: Use Dispatcher.Invoke for cross-thread operations
- **Background Processing**: Move heavy operations off the UI thread

### 5. Arabic RTL Support (Critical)
- **Primary Language**: Arabic with proper RTL layout
- **Text Flow**: Use FlowDirection="RightToLeft" for Arabic content
- **Layout Mirroring**: Ensure UI elements mirror correctly for RTL
- **Font Support**: Use appropriate Arabic fonts with proper text shaping

## File Size Guidelines
- **Small Classes**: 50-150 lines (Models, Helper classes, EventArgs)
- **Standard Classes**: 150-500 lines (ViewModels, Services, Converters) - **TARGET**
- **Large Classes**: 500-1000 lines (Complex ViewModels, large Services) - **SCRUTINIZE**
- **Oversized Classes**: >1000 lines - **MUST BE REFACTORED**

## Code Quality Standards

### Error Handling
```csharp
// Use ErrorManager for user-facing errors
try
{
    await _databaseService.ExecuteAsync(sql, parameters);
}
catch (Exception ex)
{
    ErrorManager.HandleErrorToast(ex, 
        "حدث خطأ أثناء حفظ البيانات", 
        "خطأ في قاعدة البيانات", 
        LogLevel.Error, 
        "ClientService");
}
```

### Logging Pattern
```csharp
// Use LoggingService for structured logging
LoggingService.LogDebug("Starting client creation process", "ClientService");
LoggingService.LogError($"Database error: {ex.Message}", "ClientService");
```

### Property Change Pattern
```csharp
// Use BaseViewModel's SetProperty method
private string _clientName = string.Empty;
public string ClientName
{
    get => _clientName;
    set => SetProperty(ref _clientName, value);
}
```

### Service Access Pattern
```csharp
// Use ServiceLocator for dependency injection
private readonly ClientDatabaseService _clientDatabaseService;

public MyViewModel()
{
    _clientDatabaseService = ServiceLocator.GetService<ClientDatabaseService>();
}
```

## Security Guidelines
- **SQL Injection Prevention**: Always use parameterized queries
- **Input Validation**: Validate all user inputs before processing
- **Error Messages**: Don't expose sensitive information in error messages
- **Audit Logging**: Log all data changes to Archive database

## Material Design Integration
- **Theming**: Use DynamicResource for colors and styles
- **Theme Support**: Support both Light and Dark themes
- **Component Usage**: Use MaterialDesign controls consistently
- **Styling**: Follow existing style patterns in Resources/Styles/

## Testing Approach
- **Manual Testing**: Thorough manual testing of all features
- **Performance Testing**: Monitor UI responsiveness and memory usage
- **Database Testing**: Verify data integrity and transaction handling
- **Arabic Testing**: Test with Arabic content and RTL layout

## Common Anti-Patterns to Avoid
- ❌ Direct database connections (use DatabaseService)
- ❌ Synchronous database calls on UI thread
- ❌ Business logic in ViewModels (use Services)
- ❌ Magic strings and hardcoded values
- ❌ Missing error handling and logging
- ❌ Ignoring Arabic RTL requirements
- ❌ Breaking MVVM patterns with code-behind logic

## Development Workflow
1. **Analyze**: Understand existing codebase patterns
2. **Design**: Plan the implementation following MVVM and service patterns
3. **Implement**: Write code following UFU2 conventions
4. **Test**: Manual testing with Arabic content and both themes
5. **Review**: Verify code quality and performance
6. **Document**: Update documentation if needed