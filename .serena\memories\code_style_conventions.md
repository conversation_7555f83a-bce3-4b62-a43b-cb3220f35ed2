# UFU2 Code Style and Conventions

## Naming Conventions
- **Classes**: PascalCase (e.g., `ClientDatabaseService`, `BaseViewModel`)
- **Methods**: PascalCase (e.g., `CreateClientAsync`, `ValidateData`)
- **Properties**: PascalCase (e.g., `IsLoading`, `ClientName`)
- **Private Fields**: camelCase with underscore prefix (e.g., `_databaseService`, `_isInitialized`)
- **Constants**: PascalCase (e.g., `NormalBatchIntervalMs`, `MaxBatchSize`)
- **Interfaces**: PascalCase with 'I' prefix (e.g., `IToastService`, `IWindowChromeService`)

## File Organization
- **ViewModels**: Inherit from `BaseViewModel`, end with "ViewModel" suffix
- **Services**: End with "Service" suffix, implement interfaces when appropriate
- **Models**: Plain data classes, often end with "Model" or "Data" suffix
- **Views**: XAML files with corresponding .cs code-behind (minimal logic)
- **Converters**: End with "Converter" suffix, implement `IValueConverter`

## MVVM Architecture Patterns
- **ViewModels**: Must inherit from `BaseViewModel` for smart batching
- **Commands**: Use `RelayCommand` for all UI commands
- **Data Binding**: Extensive use of WPF data binding, avoid code-behind logic
- **Service Access**: Use `ServiceLocator.GetService<T>()` for dependency injection
- **Property Changes**: Use `SetProperty()` method from BaseViewModel

## Database Patterns
- **Service Layer**: All database operations through `DatabaseService`
- **Async Operations**: All database calls must be async (`ExecuteAsync`, `QueryAsync`)
- **Parameterized Queries**: Always use parameterized queries with Dapper
- **Transactions**: Multi-step operations wrapped in database transactions
- **Error Handling**: Use `ErrorManager` for consistent exception handling

## Documentation Standards
- **XML Documentation**: Comprehensive XML comments for all public members
- **Method Documentation**: Include purpose, parameters, return values, and exceptions
- **Class Documentation**: Describe purpose, usage patterns, and key features
- **Complex Logic**: Inline comments for business logic and algorithms

## Error Handling
- **Global Handlers**: Application-level exception handlers in App.xaml.cs
- **Service Errors**: Use `ErrorManager.HandleErrorToast()` for user-facing errors
- **Logging**: Use `LoggingService` for structured logging with categories
- **Arabic Messages**: User-facing error messages in Arabic with proper RTL support

## Performance Guidelines
- **Async/Await**: Use async patterns for I/O operations and heavy computations
- **Smart Batching**: Leverage BaseViewModel's batching for property changes
- **Memory Management**: Implement `IDisposable` for resource cleanup
- **UI Thread**: Keep UI thread free with `Dispatcher.Invoke` when needed